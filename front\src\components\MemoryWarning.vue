<template>
  <div v-if="warnings.length > 0" class="memory-warning-container">
    <q-banner
      v-for="warning in warnings"
      :key="warning.id"
      class="bg-orange-2 text-orange-9 q-mb-sm"
      rounded
      dense
    >
      <template v-slot:avatar>
        <q-icon name="warning" color="orange" />
      </template>
      
      <div class="text-body2">
        <div class="text-weight-bold">{{ warning.message }}</div>
        <div v-if="warning.suggestion" class="text-caption q-mt-xs">
          建議：{{ warning.suggestion }}
        </div>
        <div v-if="warning.complexity" class="text-caption q-mt-xs">
          計算複雜度：{{ formatNumber(warning.complexity) }}
        </div>
      </div>

      <template v-slot:action>
        <q-btn
          flat
          round
          dense
          icon="close"
          @click="dismissWarning(warning.id)"
        />
      </template>
    </q-banner>

    <!-- 記憶體使用狀態 -->
    <q-card
      v-if="memoryUsage"
      class="bg-orange-1 q-mb-sm"
      flat
      bordered
    >
      <q-card-section class="q-pa-sm">
        <div class="text-caption text-orange-9 text-weight-bold q-mb-xs">
          記憶體使用狀況
        </div>
        <div class="row items-center q-gutter-sm">
          <div class="col">
            <q-linear-progress
              :value="memoryUsage.percentage / 100"
              color="orange"
              track-color="orange-2"
              size="md"
              rounded
            />
          </div>
          <div class="text-caption text-orange-9">
            {{ Math.round(memoryUsage.usedMB) }}MB / {{ Math.round(memoryUsage.limitMB) }}MB
            ({{ memoryUsage.percentage }}%)
          </div>
        </div>
      </q-card-section>
    </q-card>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { useProgressStore } from '@/stores/progress';

const progressStore = useProgressStore();

const warnings = computed(() => progressStore.warnings);
const memoryUsage = computed(() => progressStore.memoryUsage);

const dismissWarning = (id: string) => {
  const index = progressStore.warnings.findIndex(w => w.id === id);
  if (index > -1) {
    progressStore.warnings.splice(index, 1);
  }
};

const formatNumber = (num: number): string => {
  if (num >= 1000000000) {
    return (num / 1000000000).toFixed(1) + '億';
  } else if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + '百萬';
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1) + '千';
  }
  return num.toString();
};
</script>

<style scoped>
.memory-warning-container {
  margin-bottom: 16px;
}
</style>
