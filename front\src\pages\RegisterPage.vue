<template>
  <q-page>
    <q-form ref="formRef" @submit="onRegister" class="q-py-lg">
      <q-card
        class="q-mx-auto q-py-lg q-px-md"
        style="max-width: min(100%, 28rem)"
      >
        <q-card-section class="q-gutter-md">
          <div class="text-h6">使用者註冊</div>
          <q-input
            type="text"
            v-model="uid"
            label="手機號碼(帳號)"
            stack-label
            placeholder="09xxxxxxxx"
            mask="##########"
            unmasked-value
            outlined
            lazy-rules
            :rules="[
              (val) => !!val || '請輸入手機號碼',
              (val) => validatePhone(val) || '手機號碼格式錯誤',
            ]"
          />
          <q-input
            type="password"
            v-model="password"
            label="密碼"
            outlined
            lazy-rules
            :rules="[
              (val) => !!val || '請輸入密碼',
              (val) => val.length >= 4 || '密碼長度需大於 4',
            ]"
          />
          <q-input
            type="password"
            v-model="confirmPassword"
            label="確認密碼"
            outlined
            lazy-rules
            :rules="[(val) => val === password || '密碼不一致']"
          />

          <!-- 基本資料 -->
          <q-input
            type="text"
            v-model="name"
            label="姓名"
            outlined
            :rules="[(val) => !!val || '請輸入姓名']"
          />
        </q-card-section>

        <q-card-actions>
          <q-btn
            rounded
            type="submit"
            :loading="isRegistering"
            color="login"
            label="註冊"
            class="full-width q-mt-md"
            size="lg"
            :ripple="{ center: true }"
          />
        </q-card-actions>

        <q-card-actions>
          <q-btn
            dense
            flat
            to="/login"
            label="已有帳號，返回登入"
            class="full-width q-mt-md"
            size="md"
            text-color="primary"
            :ripple="{ center: true }"
            style="font-weight: bold"
          />
        </q-card-actions>
      </q-card>
    </q-form>
  </q-page>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { useRouter } from 'vue-router';
import { AUTH_API } from '@/api';
import { useDialog, validatePhone, handleError } from '@/utils';

const router = useRouter();

const formRef = ref<HTMLFormElement | null>(null);

const uid = ref('');
const password = ref('');
const confirmPassword = ref('');
const name = ref('');
const phone = ref('');
const email = ref('');
const isRegistering = ref(false);

const resetForm = () => {
  uid.value = '';
  password.value = '';
  confirmPassword.value = '';
  name.value = '';
  phone.value = '';
  email.value = '';

  formRef.value?.reset();
};

const onRegister = async () => {
  isRegistering.value = true;

  try {
    await AUTH_API.register({
      uid: uid.value,
      pwd: password.value,
      name: name.value,
      phone: phone.value,
      email: email.value,
    });

    resetForm();

    const dialog = useDialog();
    dialog.showMessage({
      title: '註冊成功',
      message: '即將前往登入頁面',
      timeout: 1500,
      color: 'positive',
      onRedirect: () => {
        router.push('/login');
      },
    });
  } catch (error) {
    handleError(error);
  } finally {
    isRegistering.value = false;
  }
};
</script>
