<template>
  <q-page>
    <q-card>
      <q-section>
        <q-table
          ref="tableRef"
          :dense="$q.screen.lt.sm"
          flat
          :rows="rows"
          :columns="columns"
          row-key="uid"
          v-model:pagination="pagination"
          :loading="isLoading"
          :filter="filter"
          :rows-per-page-options="[]"
          binary-state-sort
          @request="getData"
        >
          <!-- 搜尋列 -->
          <template v-slot:top-left>
            <q-input
              dense
              debounce="300"
              v-model="filter"
              placeholder="查詢電話、姓名"
              class="q-mb-md"
            >
              <template v-slot:append>
                <q-icon name="search" />
              </template>
            </q-input>
          </template>
          <!-- 資料表格 -->
          <template v-slot:body="props">
            <q-tr :props="props">
              <q-td key="uid" :props="props">
                {{ props.row.uid }}
              </q-td>
              <q-td key="name" :props="props">
                {{ props.row.name }}
              </q-td>
              <q-td key="is_active" :props="props">
                {{ props.row.is_active ? '啟用' : '停用' }}
                <q-popup-edit
                  v-model="props.row.is_active"
                  v-slot="scope"
                  @update:model-value="
                    updateUserActive(props.row.id, props.row.is_active)
                  "
                  buttons
                >
                  <q-select
                    v-model="scope.value"
                    label="狀態"
                    :options="[
                      { label: '啟用', value: true },
                      { label: '停用', value: false },
                    ]"
                    emit-value
                    map-options
                  />
                </q-popup-edit>
              </q-td>
              <q-td key="expires_at" :props="props" dense :auto-width="true">
                {{ props.row.expires_at }}
                <q-popup-edit
                  v-model="props.row.expires_at"
                  v-slot="scope"
                  @update:model-value="
                    updateUserExpire(props.row.id, props.row.expires_at)
                  "
                  buttons
                >
                  <q-input
                    dense
                    outlined
                    v-model="scope.value"
                    mask="date"
                    :rules="[
                      (val) =>
                        !val ||
                        (val &&
                          val.length == 10 &&
                          !isNaN(new Date(val).getTime())) ||
                        '請輸入正確的日期',
                    ]"
                    hide-bottom-space
                    style="width: 200px"
                  >
                    <template v-slot:append>
                      <q-icon name="event" class="cursor-pointer">
                        <q-popup-proxy
                          cover
                          transition-show="scale"
                          transition-hide="scale"
                        >
                          <q-date v-model="scope.value" today-btn>
                            <div class="row items-center justify-end">
                              <q-btn
                                flat
                                label="關閉"
                                color="dark"
                                v-close-popup
                              />
                            </div>
                          </q-date>
                        </q-popup-proxy>
                      </q-icon>
                    </template>
                  </q-input>
                </q-popup-edit>
              </q-td>
              <q-td key="last_login_at" :props="props">
                {{ props.row.last_login_at }}
              </q-td>
              <q-td key="created_at" :props="props">
                {{ props.row.created_at }}
              </q-td>
              <q-td key="actions" :props="props">
                <q-btn
                  dense
                  outline
                  round
                  icon="edit"
                  color="primary"
                  aria-label="Edit"
                  class="q-mr-md"
                  @click="showEditDialog(props.row)"
                />
                <q-btn
                  dense
                  outline
                  round
                  icon="delete"
                  color="red"
                  aria-label="Delete"
                  @click="deleteUser(props.row.id)"
                />
              </q-td>
            </q-tr>
          </template>

          <!-- 頁碼 -->
          <template v-slot:pagination="scope">
            <div class="q-mr-md">共 {{ pagination.rowsNumber }} 筆資料</div>

            <q-btn
              v-if="scope.pagesNumber > 2"
              icon="first_page"
              round
              dense
              flat
              :disable="scope.isFirstPage"
              @click="scope.firstPage"
            />

            <q-btn
              icon="chevron_left"
              round
              dense
              flat
              :disable="scope.isFirstPage"
              @click="scope.prevPage"
            />

            <!-- 頁數選擇 -->
            <q-select
              standard
              dense
              v-model="pagination.page"
              @update:model-value="getData({ pagination, filter })"
              :options="
                Array.from({ length: scope.pagesNumber }, (_, i) => ({
                  label: i + 1,
                  value: i + 1,
                }))
              "
              style="width: 50px"
              class="q-mx-md"
              emit-value
              map-options
            />

            <q-btn
              icon="chevron_right"
              round
              dense
              flat
              :disable="scope.isLastPage"
              @click="scope.nextPage"
            />

            <q-btn
              v-if="scope.pagesNumber > 2"
              icon="last_page"
              round
              dense
              flat
              :disable="scope.isLastPage"
              @click="scope.lastPage"
            />
          </template>
        </q-table>
      </q-section>
    </q-card>

    <!-- User資料修改 -->
    <q-dialog v-model="editDialog" persistent>
      <UserDataDialog
        v-model:visible="editDialog"
        :data="editData"
        @refreshData="getData({ pagination, filter })"
      />
    </q-dialog>
  </q-page>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { Notify } from 'quasar';
import { UserData } from '@/api/modules/user';
import USER_API from '@/api/modules/user';
import { PageProps } from '@/models/page';
import UserDataDialog from '@/components/UserDataDialog.vue';
import { useDialog, handleError } from '@/utils';

defineOptions({
  name: 'UserPage',
});

const tableRef = ref(null);

const columns = [
  {
    name: 'uid',
    label: '手機電話(帳號)',
    field: 'uid',
    align: 'left' as const,
    sortable: true,
  },
  {
    name: 'name',
    label: '姓名',
    field: 'name',
    align: 'center' as const,
    sortable: true,
  },
  {
    name: 'is_active',
    label: '狀態',
    field: 'is_active',
    format: (val: boolean) => (val ? '啟用' : '停用'),
    align: 'center' as const,
    sortable: true,
  },
  {
    name: 'expires_at',
    label: '使用期限',
    field: 'expires_at',
    align: 'center' as const,
    sortable: true,
  },
  {
    name: 'last_login_at',
    label: '最後登入時間',
    field: 'last_login_at',
    align: 'center' as const,
    sortable: true,
  },
  {
    name: 'created_at',
    label: '建立時間',
    field: 'created_at',
    align: 'center' as const,
    sortable: true,
  },
  {
    name: 'actions',
    required: true,
    label: '操作',
    field: 'actions',
    align: 'center' as const,
  },
];

const isLoading = ref(false);
const rows = ref<UserData[]>([]);
const filter = ref('');
const pagination = ref<PageProps>({
  sortBy: 'created_at',
  descending: false,
  page: 1,
  rowsPerPage: 20,
  rowsNumber: 0,
});
const pagesNumber = ref(0);

onMounted(() => {
  getData({
    pagination: pagination.value,
    filter: filter.value,
  });
});

const getData = async (props: {
  pagination: {
    page: number;
    rowsPerPage: number;
    sortBy: string;
    descending: boolean;
  };
  filter?: string;
}) => {
  try {
    isLoading.value = true;

    console.log(props.pagination);

    const { page, rowsPerPage, sortBy, descending } = props.pagination;
    pagination.value.page = page;
    pagination.value.rowsPerPage = rowsPerPage;
    pagination.value.sortBy = sortBy;
    pagination.value.descending = descending;

    const res = await USER_API.getUserList(pagination.value, filter.value);
    const { data, pageProps } = res.data;

    data.forEach((user) => {
      user.expires_at = user.expires_at
        ? new Date(user.expires_at).toLocaleDateString('zh-TW', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
          })
        : '';
      user.last_login_at = user.last_login_at
        ? new Date(user.last_login_at).toLocaleString('zh-TW', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            hour12: false,
          })
        : '尚未登入';
      user.created_at = new Date(user.created_at).toLocaleString('zh-TW', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        hour12: false,
      });
    });

    pagination.value.rowsNumber = pageProps.rowsNumber;
    pagesNumber.value = Math.ceil(
      pagination.value.rowsNumber / pagination.value.rowsPerPage
    );

    if (pagination.value.page > pagesNumber.value) {
      pagination.value.page = pagesNumber.value;
    }

    rows.value = data;
    isLoading.value = false;
  } catch (error) {
    handleError(error);
  } finally {
    isLoading.value = false;
  }
};

const updateUserActive = async (id: string, isActive: boolean) => {
  try {
    await USER_API.updateUserActive(id, isActive);
    Notify.create({
      message: isActive ? '用戶已啟用' : '用戶已停用',
      color: 'positive',
      timeout: 2000,
      position: 'top',
    });
    getData({
      pagination: pagination.value,
      filter: filter.value,
    });
  } catch (error) {
    handleError(error);
  }
};

const updateUserExpire = async (id: string, expiresAt: string) => {
  if (
    expiresAt &&
    (expiresAt.length != 10 || isNaN(new Date(expiresAt).getTime()))
  ) {
    return;
  }

  try {
    await USER_API.updateUserExpire(id, expiresAt);
    Notify.create({
      message: '用戶使用期限已更新',
      color: 'positive',
      timeout: 2000,
      position: 'top',
    });
    getData({
      pagination: pagination.value,
      filter: filter.value,
    });
  } catch (error) {
    handleError(error);
  }
};

const deleteUser = (id: string) => {
  useDialog().showMessage({
    title: '確定要刪除此用戶嗎？',
    message: '',
    timeout: 0,
    ok: async () => {
      try {
        await USER_API.deleteUser(id);
        Notify.create({
          message: '用戶已刪除',
          color: 'positive',
          timeout: 2000,
          position: 'top',
        });
        getData({
          pagination: pagination.value,
          filter: filter.value,
        });
      } catch (error) {
        handleError(error);
      }
    },
  });
};

const editDialog = ref(false);
const editData = ref<UserData>({
  id: '',
  uid: '',
  name: '',
  phone: '',
  email: '',
  is_active: true,
  expires_at: null,
  last_login_at: '',
  created_at: '',
});

const showEditDialog = (data: UserData) => {
  editData.value = data;
  editDialog.value = true;
};
</script>
