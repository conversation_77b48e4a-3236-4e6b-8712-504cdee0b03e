<template>
  <q-page class="justify-center">
    <q-card>
      <q-card-section>
        <div class="text-h4 text-center q-mb-lg">批次分析</div>

        <!-- 分析方法選擇 -->
        <div class="row q-mb-lg">
          <div class="col-12">
            <div class="text-h6 text-weight-bold q-mb-md">分析方法</div>
            <q-option-group
              v-model="selectedMethod"
              :options="analysisMethodOptions"
              color="primary"
              type="radio"
              inline
            />
          </div>
        </div>

        <!-- 彩種選擇 -->
        <div class="row q-mb-lg">
          <div class="col-12 col-sm-6">
            <div class="text-h6 text-weight-bold">彩種選擇</div>
            <div class="q-pa-sm">
              <q-select
                outlined
                dense
                v-model="selectedLottoType"
                :options="lottoTypeOptions"
                emit-value
                map-options
                @update:model-value="onLottoTypeChange"
              />
            </div>
          </div>
          <div class="col-12 col-sm-6">
            <div class="text-h6 text-weight-bold">參考期號</div>
            <div class="q-pa-sm">
              <q-input
                outlined
                dense
                v-model="referenceDate"
                type="date"
                :max="maxDate"
              />
            </div>
          </div>
        </div>

        <!-- 批次設定 -->
        <div class="row q-mb-lg">
          <div class="col-12 col-sm-6">
            <div class="text-h6 text-weight-bold">推算期數</div>
            <div class="q-pa-sm">
              <q-input
                outlined
                dense
                v-model.number="batchPeriods"
                type="number"
                min="1"
                max="100"
                hint="將產生等數量的計算結果"
              />
            </div>
          </div>
          <div class="col-12 col-sm-6">
            <div class="text-h6 text-weight-bold">輸出格式</div>
            <div class="q-pa-sm">
              <q-select
                outlined
                dense
                v-model="outputFormat"
                :options="outputFormatOptions"
                emit-value
                map-options
              />
            </div>
          </div>
        </div>

        <!-- 動態參數設定區域 -->
        <div class="row q-mb-lg" v-if="selectedMethod">
          <div class="col-12">
            <div class="text-h6 text-weight-bold q-mb-md">參數設定</div>

            <!-- 拖牌分析參數 -->
            <template v-if="selectedMethod === 'ball-follow'">
              <div class="row q-mb-md">
                <div class="col-12 text-subtitle1 text-weight-bold">拖牌組合</div>
                <div class="col-12 col-sm-4 q-pa-sm">
                  <q-select
                    outlined
                    dense
                    v-model="ballFollowParams.num1"
                    :options="numOpts"
                    emit-value
                    map-options
                    label="第一組"
                  />
                </div>
                <div class="col-12 col-sm-4 q-pa-sm">
                  <q-select
                    outlined
                    dense
                    v-model="ballFollowParams.num2"
                    :options="numOpts"
                    emit-value
                    map-options
                    label="第二組"
                  />
                </div>
                <div class="col-12 col-sm-4 q-pa-sm">
                  <q-select
                    outlined
                    dense
                    v-model="ballFollowParams.num3"
                    :options="numOpts"
                    emit-value
                    map-options
                    label="目標組"
                  />
                </div>
              </div>
              <div class="row q-mb-md">
                <div class="col-12 col-sm-4 q-pa-sm">
                  <q-select
                    outlined
                    dense
                    v-model="ballFollowParams.periodNum"
                    :options="periodNumOpts"
                    emit-value
                    map-options
                    label="推算期數"
                  />
                </div>
                <div class="col-12 col-sm-4 q-pa-sm">
                  <q-select
                    outlined
                    dense
                    v-model="ballFollowParams.maxRange"
                    :options="maxRangeOpts"
                    emit-value
                    map-options
                    label="最大區間"
                  />
                </div>
                <div class="col-12 col-sm-4 q-pa-sm">
                  <q-select
                    outlined
                    dense
                    v-model="ballFollowParams.aheadNum"
                    :options="aheadNumOpts"
                    emit-value
                    map-options
                    label="預測期數"
                  />
                </div>
              </div>
            </template>

            <!-- 尾數分析參數 -->
            <template v-if="selectedMethod === 'tail'">
              <div class="row q-mb-md">
                <div class="col-12 text-subtitle1 text-weight-bold">尾數拖牌組合</div>
                <div class="col-12 col-sm-4 q-pa-sm">
                  <q-select
                    outlined
                    dense
                    v-model="tailParams.num1"
                    :options="numOpts"
                    emit-value
                    map-options
                    label="第一組"
                  />
                </div>
                <div class="col-12 col-sm-4 q-pa-sm">
                  <q-select
                    outlined
                    dense
                    v-model="tailParams.num2"
                    :options="numOpts"
                    emit-value
                    map-options
                    label="第二組"
                  />
                </div>
                <div class="col-12 col-sm-4 q-pa-sm">
                  <q-select
                    outlined
                    dense
                    v-model="tailParams.num3"
                    :options="numOpts"
                    emit-value
                    map-options
                    label="目標組"
                  />
                </div>
              </div>
              <div class="row q-mb-md">
                <div class="col-12 col-sm-4 q-pa-sm">
                  <q-select
                    outlined
                    dense
                    v-model="tailParams.periodNum"
                    :options="periodNumOpts"
                    emit-value
                    map-options
                    label="推算期數"
                  />
                </div>
                <div class="col-12 col-sm-4 q-pa-sm">
                  <q-select
                    outlined
                    dense
                    v-model="tailParams.maxRange"
                    :options="maxRangeOpts"
                    emit-value
                    map-options
                    label="最大區間"
                  />
                </div>
                <div class="col-12 col-sm-4 q-pa-sm">
                  <q-select
                    outlined
                    dense
                    v-model="tailParams.aheadNum"
                    :options="aheadNumOpts"
                    emit-value
                    map-options
                    label="預測期數"
                  />
                </div>
              </div>
            </template>

            <!-- 版路分析參數 -->
            <template v-if="selectedMethod === 'pattern'">
              <div class="row q-mb-md">
                <div class="col-12 text-subtitle1 text-weight-bold">拖牌組合</div>
                <div class="col-12 col-sm-4 q-pa-sm">
                  <q-select
                    outlined
                    dense
                    v-model="patternParams.comb1"
                    :options="combOptions"
                    emit-value
                    map-options
                    label="組合1"
                  />
                </div>
                <div class="col-12 col-sm-4 q-pa-sm">
                  <q-select
                    outlined
                    dense
                    v-model="patternParams.comb2"
                    :options="combOptions"
                    emit-value
                    map-options
                    label="組合2"
                  />
                </div>
                <div class="col-12 col-sm-4 q-pa-sm">
                  <q-select
                    outlined
                    dense
                    v-model="patternParams.comb3"
                    :options="combOptions"
                    emit-value
                    map-options
                    label="組合3"
                  />
                </div>
              </div>
              <div class="row q-mb-md">
                <div class="col-12 text-subtitle1 text-weight-bold">尾數拖牌組合</div>
                <div class="col-12 col-sm-4 q-pa-sm">
                  <q-select
                    outlined
                    dense
                    v-model="patternParams.tailComb1"
                    :options="tailCombOptions"
                    emit-value
                    map-options
                    label="尾數組合1"
                  />
                </div>
                <div class="col-12 col-sm-4 q-pa-sm">
                  <q-select
                    outlined
                    dense
                    v-model="patternParams.tailComb2"
                    :options="tailCombOptions"
                    emit-value
                    map-options
                    label="尾數組合2"
                  />
                </div>
                <div class="col-12 col-sm-4 q-pa-sm">
                  <q-select
                    outlined
                    dense
                    v-model="patternParams.tailComb3"
                    :options="tailCombOptions"
                    emit-value
                    map-options
                    label="尾數組合3"
                  />
                </div>
              </div>
              <div class="row q-mb-md">
                <div class="col-12 col-sm-4 q-pa-sm">
                  <q-select
                    outlined
                    dense
                    v-model="patternParams.period"
                    :options="periodOptions"
                    emit-value
                    map-options
                    label="推算期數"
                  />
                </div>
                <div class="col-12 col-sm-4 q-pa-sm">
                  <q-select
                    outlined
                    dense
                    v-model="patternParams.ahead"
                    :options="aheadNumOpts"
                    emit-value
                    map-options
                    label="預測期數"
                  />
                </div>
              </div>
            </template>
          </div>
        </div>

        <!-- 操作按鈕 -->
        <q-card-actions align="right" class="q-my-lg q-py-none q-px-md">
          <q-btn
            type="button"
            label="中斷計算"
            color="negative"
            @click="stopBatchAnalysis"
            class="text-h6 q-mr-md"
            v-if="isCalculating"
          />
          <q-btn
            type="button"
            label="開始批次分析"
            color="positive"
            class="text-h6 q-px-lg q-py-sm"
            @click="startBatchAnalysis"
            :loading="isCalculating"
            :disable="!canStartAnalysis"
          >
            <template v-slot:loading>
              <q-spinner-dots />
            </template>
          </q-btn>
        </q-card-actions>
      </q-card-section>

      <!-- 進度條 -->
      <q-card-section v-if="isCalculating">
        <div class="text-center q-mb-sm">
          {{ progressMessage }}
        </div>
        <q-linear-progress
          rounded
          size="md"
          :value="progress"
          :animation-speed="50"
          color="primary"
          class="q-mb-xs"
        />
      </q-card-section>
    </q-card>

    <!-- 結果顯示區域 -->
    <q-card v-if="batchResults.length > 0" class="q-mt-lg">
      <q-card-section>
        <div class="text-h6 text-weight-bold q-mb-md">批次分析結果</div>
        <div class="row q-mb-md">
          <div class="col-12 col-sm-6">
            <div class="text-subtitle1">
              分析方法：{{ getMethodName(selectedMethod) }}
            </div>
            <div class="text-subtitle1">
              彩種：{{ getLottoTypeName(selectedLottoType) }}
            </div>
          </div>
          <div class="col-12 col-sm-6 text-right">
            <q-btn
              color="primary"
              icon="download"
              :label="`下載 ${outputFormat.toUpperCase()} 檔案`"
              @click="downloadResults"
              :disable="batchResults.length === 0"
            />
          </div>
        </div>

        <!-- 結果預覽表格 -->
        <q-table
          :rows="batchResults.slice(0, 10)"
          :columns="resultColumns"
          row-key="period"
          dense
          flat
          bordered
        >
          <template v-slot:bottom v-if="batchResults.length > 10">
            <div class="text-center q-pa-md text-grey-6">
              顯示前 10 筆結果，完整結果請下載檔案查看
            </div>
          </template>
        </q-table>
      </q-card-section>
    </q-card>
  </q-page>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted } from 'vue';
import { useLottoStore } from '@/stores/lotto';
import { useProgressStore } from '@/stores/progress';
import { LOTTO_API } from '@/api';
import { Notify } from 'quasar';
import * as XLSX from 'xlsx';

// Stores
const lottoStore = useLottoStore();
const progressStore = useProgressStore();

// 分析方法選項
const analysisMethodOptions = [
  { label: '拖牌分析', value: 'ball-follow' },
  { label: '尾數分析', value: 'tail' },
  { label: '版路分析', value: 'pattern' }
];

// 彩種選項
const lottoTypeOptions = [
  { label: '威力彩', value: 'superlotto638' },
  { label: '大樂透', value: 'lotto649' },
  { label: '今彩539', value: 'daily539' },
  { label: '香港六合彩', value: 'hklotto' }
];

// 輸出格式選項
const outputFormatOptions = [
  { label: 'Excel (.xlsx)', value: 'excel' },
  { label: 'CSV (.csv)', value: 'csv' }
];

// 響應式數據
const selectedMethod = ref('');
const selectedLottoType = ref('superlotto638');
const referenceDate = ref('');
const batchPeriods = ref(50);
const outputFormat = ref('excel');
const isCalculating = ref(false);
const progress = ref(0);
const progressMessage = ref('');
const batchResults = ref<any[]>([]);

// 當前日期（用於限制參考期號）
const maxDate = computed(() => {
  return new Date().toISOString().split('T')[0];
});

// 檢查是否可以開始分析
const canStartAnalysis = computed(() => {
  return selectedMethod.value &&
         selectedLottoType.value &&
         referenceDate.value &&
         batchPeriods.value > 0 &&
         !isCalculating.value;
});

// 拖牌分析參數
const ballFollowParams = ref({
  num1: 1,
  num2: 1,
  num3: 1,
  periodNum: 50,
  maxRange: 20,
  aheadNum: 1
});

// 尾數分析參數
const tailParams = ref({
  num1: 1,
  num2: 1,
  num3: 1,
  periodNum: 50,
  maxRange: 20,
  aheadNum: 1
});

// 版路分析參數
const patternParams = ref({
  comb1: 1,
  comb2: 1,
  comb3: 1,
  tailComb1: 1,
  tailComb2: 1,
  tailComb3: 1,
  period: 50,
  ahead: 1
});

// 選項數據
const numOpts = ref(
  Array.from({ length: 10 }, (_, i) => ({
    label: `${i + 1}個`,
    value: i + 1,
  }))
);

const periodNumOpts = ref(
  Array.from({ length: 491 }, (_, i) => ({
    label: `${i + 10}期`,
    value: i + 10,
  }))
);

const maxRangeOpts = ref(
  Array.from({ length: 80 }, (_, i) => ({
    label: `${i + 1}期`,
    value: i + 1,
  }))
);

const aheadNumOpts = ref(
  Array.from({ length: 10 }, (_, i) => ({
    label: `${i + 1}期`,
    value: i + 1,
  }))
);

const combOptions = ref(
  Array.from({ length: 10 }, (_, i) => ({
    label: `${i + 1}個`,
    value: i + 1,
  }))
);

const tailCombOptions = ref(
  Array.from({ length: 10 }, (_, i) => ({
    label: `${i + 1}個`,
    value: i + 1,
  }))
);

const periodOptions = ref(
  Array.from({ length: 991 }, (_, i) => ({
    label: `${i + 10}期`,
    value: i + 10,
  }))
);

// 結果表格欄位
const resultColumns = computed(() => {
  const baseColumns = [
    { name: 'period', label: '期號', field: 'period', align: 'left' },
    { name: 'date', label: '日期', field: 'date', align: 'left' },
    { name: 'method', label: '分析方法', field: 'method', align: 'left' }
  ];

  if (selectedMethod.value === 'ball-follow') {
    return [
      ...baseColumns,
      { name: 'firstNumbers', label: '第一組號碼', field: 'firstNumbers', align: 'left' },
      { name: 'secondNumbers', label: '第二組號碼', field: 'secondNumbers', align: 'left' },
      { name: 'targetNumbers', label: '預測號碼', field: 'targetNumbers', align: 'left' },
      { name: 'probability', label: '機率', field: 'probability', align: 'right' }
    ];
  } else if (selectedMethod.value === 'tail') {
    return [
      ...baseColumns,
      { name: 'tailNumbers', label: '尾數組合', field: 'tailNumbers', align: 'left' },
      { name: 'predictTails', label: '預測尾數', field: 'predictTails', align: 'left' },
      { name: 'probability', label: '機率', field: 'probability', align: 'right' }
    ];
  } else {
    return [
      ...baseColumns,
      { name: 'pattern', label: '版路模式', field: 'pattern', align: 'left' },
      { name: 'prediction', label: '預測結果', field: 'prediction', align: 'left' },
      { name: 'confidence', label: '信心度', field: 'confidence', align: 'right' }
    ];
  }
});

// 方法
const getMethodName = (method: string) => {
  const methodMap: Record<string, string> = {
    'ball-follow': '拖牌分析',
    'tail': '尾數分析',
    'pattern': '版路分析'
  };
  return methodMap[method] || method;
};

const getLottoTypeName = (type: string) => {
  const typeMap: Record<string, string> = {
    'superlotto638': '威力彩',
    'lotto649': '大樂透',
    'daily539': '今彩539',
    'hklotto': '香港六合彩'
  };
  return typeMap[type] || type;
};

const onLottoTypeChange = () => {
  // 當彩種改變時，可以在這裡更新相關設定
  batchResults.value = [];
};

const startBatchAnalysis = async () => {
  if (!canStartAnalysis.value) {
    Notify.create({
      type: 'warning',
      message: '請完整填寫所有必要參數'
    });
    return;
  }

  try {
    isCalculating.value = true;
    progress.value = 0;
    progressMessage.value = '準備開始批次分析...';
    batchResults.value = [];

    // 模擬批次分析過程
    for (let i = 0; i < batchPeriods.value; i++) {
      progressMessage.value = `正在分析第 ${i + 1} / ${batchPeriods.value} 期...`;
      progress.value = (i + 1) / batchPeriods.value;

      // 模擬分析延遲
      await new Promise(resolve => setTimeout(resolve, 100));

      // 生成模擬結果
      const result = generateMockResult(i);
      batchResults.value.push(result);
    }

    progressMessage.value = '批次分析完成！';

    Notify.create({
      type: 'positive',
      message: `批次分析完成！共產生 ${batchResults.value.length} 筆結果`
    });

  } catch (error) {
    console.error('批次分析失敗:', error);
    Notify.create({
      type: 'negative',
      message: '批次分析失敗，請稍後再試'
    });
  } finally {
    isCalculating.value = false;
  }
};

const stopBatchAnalysis = () => {
  isCalculating.value = false;
  progressMessage.value = '分析已中斷';

  Notify.create({
    type: 'warning',
    message: '批次分析已中斷'
  });
};

const generateMockResult = (index: number) => {
  const baseDate = new Date(referenceDate.value);
  baseDate.setDate(baseDate.getDate() - index);

  const result = {
    period: `${baseDate.getFullYear()}${String(baseDate.getMonth() + 1).padStart(2, '0')}${String(baseDate.getDate()).padStart(2, '0')}`,
    date: baseDate.toISOString().split('T')[0],
    method: getMethodName(selectedMethod.value)
  };

  if (selectedMethod.value === 'ball-follow') {
    return {
      ...result,
      firstNumbers: Array.from({length: ballFollowParams.value.num1}, () => Math.floor(Math.random() * 49) + 1).join(', '),
      secondNumbers: Array.from({length: ballFollowParams.value.num2}, () => Math.floor(Math.random() * 49) + 1).join(', '),
      targetNumbers: Array.from({length: ballFollowParams.value.num3}, () => Math.floor(Math.random() * 49) + 1).join(', '),
      probability: (Math.random() * 100).toFixed(2) + '%'
    };
  } else if (selectedMethod.value === 'tail') {
    return {
      ...result,
      tailNumbers: Array.from({length: tailParams.value.num1}, () => Math.floor(Math.random() * 10)).join(', '),
      predictTails: Array.from({length: tailParams.value.num3}, () => Math.floor(Math.random() * 10)).join(', '),
      probability: (Math.random() * 100).toFixed(2) + '%'
    };
  } else {
    return {
      ...result,
      pattern: `模式${Math.floor(Math.random() * 10) + 1}`,
      prediction: Array.from({length: 6}, () => Math.floor(Math.random() * 49) + 1).join(', '),
      confidence: (Math.random() * 100).toFixed(2) + '%'
    };
  }
};

const downloadResults = () => {
  if (batchResults.value.length === 0) {
    Notify.create({
      type: 'warning',
      message: '沒有可下載的結果'
    });
    return;
  }

  try {
    if (outputFormat.value === 'csv') {
      downloadCSV();
    } else {
      downloadExcel();
    }
  } catch (error) {
    console.error('下載失敗:', error);
    Notify.create({
      type: 'negative',
      message: '檔案下載失敗'
    });
  }
};

const downloadCSV = () => {
  const headers = resultColumns.value.map(col => col.label).join(',');
  const rows = batchResults.value.map(row =>
    resultColumns.value.map(col => row[col.field] || '').join(',')
  );

  const csvContent = [headers, ...rows].join('\n');
  const blob = new Blob(['\uFEFF' + csvContent], { type: 'text/csv;charset=utf-8;' });

  const link = document.createElement('a');
  link.href = URL.createObjectURL(blob);
  link.download = `批次分析結果_${selectedMethod.value}_${new Date().toISOString().split('T')[0]}.csv`;
  link.click();

  Notify.create({
    type: 'positive',
    message: 'CSV 檔案下載完成'
  });
};

const downloadExcel = () => {
  try {
    // 準備工作表數據
    const worksheetData = [
      // 標題行
      resultColumns.value.map(col => col.label),
      // 數據行
      ...batchResults.value.map(row =>
        resultColumns.value.map(col => row[col.field] || '')
      )
    ];

    // 創建工作表
    const worksheet = XLSX.utils.aoa_to_sheet(worksheetData);

    // 設置列寬
    const colWidths = resultColumns.value.map(col => {
      if (col.name === 'period' || col.name === 'date') return { wch: 12 };
      if (col.name === 'method') return { wch: 15 };
      if (col.name.includes('Numbers') || col.name.includes('prediction')) return { wch: 25 };
      return { wch: 15 };
    });
    worksheet['!cols'] = colWidths;

    // 創建工作簿
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, '批次分析結果');

    // 添加分析參數工作表
    const paramData = [
      ['分析參數', ''],
      ['分析方法', getMethodName(selectedMethod.value)],
      ['彩種', getLottoTypeName(selectedLottoType.value)],
      ['參考期號', referenceDate.value],
      ['推算期數', batchPeriods.value.toString()],
      ['', ''],
      ['詳細參數', '']
    ];

    if (selectedMethod.value === 'ball-follow') {
      paramData.push(
        ['第一組號碼數', ballFollowParams.value.num1.toString()],
        ['第二組號碼數', ballFollowParams.value.num2.toString()],
        ['目標組號碼數', ballFollowParams.value.num3.toString()],
        ['推算期數', ballFollowParams.value.periodNum.toString()],
        ['最大區間', ballFollowParams.value.maxRange.toString()],
        ['預測期數', ballFollowParams.value.aheadNum.toString()]
      );
    } else if (selectedMethod.value === 'tail') {
      paramData.push(
        ['第一組尾數數', tailParams.value.num1.toString()],
        ['第二組尾數數', tailParams.value.num2.toString()],
        ['目標組尾數數', tailParams.value.num3.toString()],
        ['推算期數', tailParams.value.periodNum.toString()],
        ['最大區間', tailParams.value.maxRange.toString()],
        ['預測期數', tailParams.value.aheadNum.toString()]
      );
    } else if (selectedMethod.value === 'pattern') {
      paramData.push(
        ['拖牌組合1', patternParams.value.comb1.toString()],
        ['拖牌組合2', patternParams.value.comb2.toString()],
        ['拖牌組合3', patternParams.value.comb3.toString()],
        ['尾數組合1', patternParams.value.tailComb1.toString()],
        ['尾數組合2', patternParams.value.tailComb2.toString()],
        ['尾數組合3', patternParams.value.tailComb3.toString()],
        ['推算期數', patternParams.value.period.toString()],
        ['預測期數', patternParams.value.ahead.toString()]
      );
    }

    const paramWorksheet = XLSX.utils.aoa_to_sheet(paramData);
    paramWorksheet['!cols'] = [{ wch: 20 }, { wch: 15 }];
    XLSX.utils.book_append_sheet(workbook, paramWorksheet, '分析參數');

    // 生成檔案名稱
    const fileName = `批次分析結果_${getMethodName(selectedMethod.value)}_${getLottoTypeName(selectedLottoType.value)}_${new Date().toISOString().split('T')[0]}.xlsx`;

    // 下載檔案
    XLSX.writeFile(workbook, fileName);

    Notify.create({
      type: 'positive',
      message: 'Excel 檔案下載完成'
    });
  } catch (error) {
    console.error('Excel 下載失敗:', error);
    Notify.create({
      type: 'negative',
      message: 'Excel 檔案下載失敗'
    });
  }
};

// 初始化
onMounted(() => {
  // 設定預設參考日期為今天
  referenceDate.value = new Date().toISOString().split('T')[0];
});
</script>

<style lang="scss" scoped>
.q-card {
  max-width: 1200px;
  margin: 0 auto;
}

.q-option-group {
  .q-radio {
    margin-right: 2rem;
  }
}
</style>
