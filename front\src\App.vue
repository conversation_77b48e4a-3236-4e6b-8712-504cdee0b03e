<template>
  <router-view />
</template>

<script lang="ts" setup>
import { onMounted } from 'vue';
import { pwaInstallService } from './services/pwaInstall';

// 在應用啟動時初始化
onMounted(() => {
  console.log('初始化 PWA 安裝服務');
  pwaInstallService.initialize();

  // 監聽來自 Service Worker 的消息
  if ('serviceWorker' in navigator) {
    navigator.serviceWorker.addEventListener('message', (event) => {
      if (event.data && event.data.type === 'RELOAD_PAGE') {
        console.log('收到 Service Worker 重新載入頁面的請求');
        window.location.reload();
      }
    });
  }
});

defineOptions({
  name: 'App',
});
</script>
