import {
  DrawResult,
  AnalysisConfig,
  StatResult,
  ProgressInfo,
  Occurrence,
  WarningInfo,
} from '@/models/types';

interface AnalysisResponse {
  data: StatResult[];
  occurrences: Map<string, Occurrence>;
  matchData: Map<number, StatResult[]>; // 用於分析結果匹配
}

export class LottoAnalyzer {
  private worker: Worker | null = null;
  private isAnalyzing = false;
  private warningCallback: ((warning: WarningInfo) => void) | null = null;

  constructor(private drawResults: DrawResult[]) {}

  public async analyzeBatch(config: AnalysisConfig): Promise<AnalysisResponse> {
    if (this.isAnalyzing) {
      throw new Error('Analysis is already in progress');
    }

    this.isAnalyzing = true;

    return new Promise((resolve, reject) => {
      try {
        // 創建 Worker
        this.worker = new Worker(
          new URL('../workers/analyzer.worker.ts', import.meta.url),
          {
            type: 'module',
          }
        );

        // 設置 Worker 事件處理
        this.worker.onmessage = (e) => {
          const { type, data, occurrences, matchData } = e.data;

          switch (type) {
            case 'progress':
              if (this.progressCallback) {
                this.progressCallback(data);
              }
              break;

            case 'warning':
              if (this.warningCallback) {
                this.warningCallback(data);
              }
              console.warn('Worker 警告:', data);
              break;

            case 'log':
              console.log(data);
              break;

            case 'debug':
              console.log('計算統計信息:', data);
              break;

            case 'complete':
              if (this.progressCallback) {
                this.progressCallback(data);
              }
              this.cleanupWorker();
              this.isAnalyzing = false;
              resolve({
                data,
                occurrences,
                matchData,
              });
              break;

            case 'error':
              this.cleanupWorker();
              this.isAnalyzing = false;
              reject(new Error(data.message));
              break;
          }
        };

        // 發送初始化數據給 Worker
        this.worker.postMessage({
          type: 'init',
          data: {
            results: JSON.stringify(this.drawResults),
            config: JSON.stringify(config),
          },
        });
      } catch (error) {
        this.cleanupWorker();
        this.isAnalyzing = false;
        reject(error);
      }
    });
  }

  private progressCallback: ((progress: ProgressInfo) => void) | null = null;

  public setProgressCallback(callback: (progress: ProgressInfo) => void) {
    this.progressCallback = callback;
  }

  public setWarningCallback(callback: (warning: WarningInfo) => void) {
    this.warningCallback = callback;
  }

  private cleanupWorker() {
    if (this.worker) {
      this.worker.terminate();
      this.worker = null;
    }
  }

  public stopWorker() {
    this.cleanupWorker();
    this.isAnalyzing = false;
  }
}
