<template>
  <q-dialog v-model="visible">
    <q-card style="max-width: 100%; width: 800px">
      <q-card-section class="row items-center">
        <div class="text-h6">詳細資料</div>
        <q-space />
        <q-btn icon="close" flat round dense @click="visible = false" />
      </q-card-section>

      <q-card-section class="q-pa-md">
        <div class="text-h6 q-mb-md">
          開出
          <q-chip
            v-for="number in selectedDetail?.firstNumbers"
            :key="number"
            color="primary"
            text-color="white"
            class="text-h6"
          >
            {{ number }}尾
          </q-chip>
          下{{ selectedDetail?.gap }}期開
          <q-chip
            v-for="number in selectedDetail?.secondNumbers"
            :key="number"
            color="secondary"
            text-color="white"
            class="text-h6"
          >
            {{ number }}尾
          </q-chip>

          再下 {{ selectedDetail?.targetGap }} 期預測拖出
          <q-chip
            v-for="number in selectedDetail?.targetNumbers"
            :key="number"
            color="accent"
            text-color="white"
            class="text-h6"
          >
            {{ number }}尾
          </q-chip>
        </div>

        <div class="text-h6 q-mb-md">
          開出{{ getStatOccurrence(selectedDetail) }}次，共準{{
            selectedDetail?.targetMatches ?? 0
          }}次，準確率:
          {{ ((selectedDetail?.targetProbability ?? 0) * 100).toFixed(2) }}%
        </div>

        <div
          class="reverse-order"
          style="display: flex; flex-direction: column-reverse"
        >
          <template
            v-for="(detail, index) in getStatPeriods(selectedDetail)"
            :key="index"
          >
            <q-card class="q-mb-md">
              <q-card-section>
                <!-- 第一個組合 -->
                <div class="row text-h6 q-mb-md">
                  <div class="col-auto">
                    <div>第 {{ detail.firstPeriod }} 期</div>
                    <div>
                      開獎日期：{{ getDrawDateByPeriod(detail.firstPeriod) }}
                    </div>
                  </div>
                  <div class="row balls">
                    <div
                      class="col-auto"
                      v-for="(
                        number, index
                      ) in getDrawNumbersAndSpecialNumberByPeriod(
                        detail.firstPeriod
                      ).numbers"
                      :key="index"
                    >
                      <div
                        class="ball"
                        :class="{
                          'first-num': selectedDetail?.firstNumbers.includes(
                            number % 10
                          ),
                        }"
                      >
                        {{ paddingZero(number) }}
                      </div>
                    </div>
                    <div
                      class="col-auto"
                      v-if="
                        getDrawNumbersAndSpecialNumberByPeriod(
                          detail.firstPeriod
                        ).specialNumber
                      "
                    >
                      <div
                        class="ball special-number"
                        :class="{
                          'first-num':
                            selectedDetail?.firstNumbers.includes(
                              getDrawNumbersAndSpecialNumberByPeriod(
                                detail.firstPeriod
                              ).specialNumber % 10
                            ) && !lottoStore.isSuperLotto,
                        }"
                      >
                        {{
                          paddingZero(
                            getDrawNumbersAndSpecialNumberByPeriod(
                              detail.firstPeriod
                            ).specialNumber
                          )
                        }}
                      </div>
                    </div>
                  </div>
                </div>
                <!-- 第二個組合 -->
                <div class="row text-h6 q-mb-md" v-if="detail.secondPeriod">
                  <div class="col-auto">
                    <div>第 {{ detail.secondPeriod }} 期</div>
                    <div>
                      開獎日期：{{
                        getDrawDateByPeriod(detail.secondPeriod) ?? '未開獎'
                      }}
                    </div>
                  </div>
                  <div class="row balls">
                    <div
                      class="col-auto"
                      v-for="(
                        number, index
                      ) in getDrawNumbersAndSpecialNumberByPeriod(
                        detail.secondPeriod
                      ).numbers"
                      :key="index"
                    >
                      <div
                        class="ball"
                        :class="{
                          'second-num': selectedDetail?.secondNumbers.includes(
                            number % 10
                          ),
                        }"
                      >
                        {{ paddingZero(number) }}
                      </div>
                    </div>
                    <div
                      class="col-auto"
                      v-if="
                        getDrawNumbersAndSpecialNumberByPeriod(
                          detail.secondPeriod
                        ).specialNumber
                      "
                    >
                      <div
                        class="ball special-number"
                        :class="{
                          'second-num':
                            selectedDetail?.secondNumbers.includes(
                              getDrawNumbersAndSpecialNumberByPeriod(
                                detail.secondPeriod
                              ).specialNumber % 10
                            ) && !lottoStore.isSuperLotto,
                        }"
                      >
                        {{
                          paddingZero(
                            getDrawNumbersAndSpecialNumberByPeriod(
                              detail.secondPeriod
                            ).specialNumber
                          )
                        }}
                      </div>
                    </div>
                  </div>
                </div>
                <!-- 目標組合 -->
                <div class="row text-h6" v-if="detail.targetPeriod">
                  <div class="col-auto">
                    <div>第 {{ detail.targetPeriod }} 期</div>
                    <div>
                      開獎日期：{{
                        getDrawDateByPeriod(detail.targetPeriod) ?? '未開獎'
                      }}
                    </div>
                  </div>
                  <div class="row balls">
                    <div
                      class="col-auto"
                      v-for="(
                        number, index
                      ) in getDrawNumbersAndSpecialNumberByPeriod(
                        detail.targetPeriod
                      ).numbers"
                      :key="index"
                    >
                      <div
                        class="ball"
                        :class="{
                          'target-num': selectedDetail?.targetNumbers.includes(
                            number % 10
                          ),
                        }"
                      >
                        {{ paddingZero(number) }}
                      </div>
                    </div>
                    <div
                      class="col-auto"
                      v-if="
                        getDrawNumbersAndSpecialNumberByPeriod(
                          detail.targetPeriod
                        ).specialNumber
                      "
                    >
                      <div
                        class="ball special-number"
                        :class="{
                          'target-num':
                            selectedDetail?.targetNumbers.includes(
                              getDrawNumbersAndSpecialNumberByPeriod(
                                detail.targetPeriod
                              ).specialNumber % 10
                            ) && !lottoStore.isSuperLotto,
                        }"
                      >
                        {{
                          paddingZero(
                            getDrawNumbersAndSpecialNumberByPeriod(
                              detail.targetPeriod
                            ).specialNumber
                          )
                        }}
                      </div>
                    </div>
                  </div>
                </div>
              </q-card-section>
            </q-card>
          </template>
        </div>
      </q-card-section>
    </q-card>
  </q-dialog>
</template>

<script lang="ts" setup>
import { computed } from 'vue';
import { useLottoStore } from '@/stores/lotto';
import { LottoItem } from '@/api/modules/lotto';
import { StatResult, Occurrence } from '@/models/types';
import { paddingZero } from '@/utils';

const props = defineProps<{
  modelValue: boolean;
  results: LottoItem[];
  selectedDetail: StatResult | null;
  occurrences: Map<string, Occurrence>;
}>();

const lottoStore = useLottoStore();

const visible = computed({
  get: () => props.modelValue,
  set: (value: boolean) => emit('update:modelValue', value),
});

const emit = defineEmits(['update:modelValue']);

const getDrawDateByPeriod = (period: string) => {
  if (!period) return '';
  const result = props.results.find((item) => item.period == period);
  return result?.draw_date;
};

const getDrawNumbersAndSpecialNumberByPeriod = (period: string) => {
  if (!period) return { numbers: [], specialNumber: 0 };

  const result = props.results.find((item) => item.period == period);
  return {
    numbers: result?.draw_number_size,
    specialNumber: result?.special_number ? Number(result.special_number) : 0,
  };
};

const getStatOccurrence = (stat: StatResult | null) => {
  if (!stat) return 0;

  // const key = stat.firstNumbers.join(',') + '-' + stat.targetGap;
  const key =
    stat.firstNumbers.join(',') +
    '-' +
    stat.secondNumbers.join(',') +
    '-' +
    stat.gap +
    '-' +
    stat.targetGap;

  return props.occurrences.get(key)?.count ?? 0;
};

const getStatPeriods = (stat: StatResult | null) => {
  if (!stat) return [];

  // const key = stat.firstNumbers.join(',') + '-' + stat.targetGap;
  const key =
    stat.firstNumbers.join(',') +
    '-' +
    stat.secondNumbers.join(',') +
    '-' +
    stat.gap +
    '-' +
    stat.targetGap;

  return props.occurrences.get(key)?.periods ?? [];
};
</script>

<style lang="scss" scoped>
.balls {
  margin: auto;
}

.ball {
  width: 2.5rem;
  height: 2.5rem;

  font-size: 1.2rem;

  &.first-num {
    background-color: $primary;
    color: white;
  }

  &.second-num {
    background-color: $secondary;
    color: white;
  }

  &.target-num {
    background-color: $accent;
    color: white;
  }
}
</style>
