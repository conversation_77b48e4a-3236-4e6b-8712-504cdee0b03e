package controllers

import (
	"fmt"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"

	. "lottery/database"
	. "lottery/models"
	. "lottery/utils"
)

// BatchAnalysisRequest 批次分析請求結構
type BatchAnalysisRequest struct {
	Method          string                 `json:"method" binding:"required"`           // 分析方法: ball-follow, tail, pattern
	LottoType       string                 `json:"lotto_type" binding:"required"`       // 彩種
	ReferenceDate   string                 `json:"reference_date" binding:"required"`   // 參考期號日期
	BatchPeriods    int                    `json:"batch_periods" binding:"required"`    // 推算期數
	Parameters      map[string]interface{} `json:"parameters" binding:"required"`       // 分析參數
}

// BatchAnalysisResult 批次分析結果結構
type BatchAnalysisResult struct {
	Period     string                 `json:"period"`
	Date       string                 `json:"date"`
	Method     string                 `json:"method"`
	LottoType  string                 `json:"lotto_type"`
	Parameters map[string]interface{} `json:"parameters"`
	Results    map[string]interface{} `json:"results"`
}

// BatchAnalysisResponse 批次分析響應結構
type BatchAnalysisResponse struct {
	Success     bool                  `json:"success"`
	Message     string                `json:"message"`
	TotalCount  int                   `json:"total_count"`
	Results     []BatchAnalysisResult `json:"results"`
	GeneratedAt time.Time             `json:"generated_at"`
}

// StartBatchAnalysis 開始批次分析
func StartBatchAnalysis(c *gin.Context) {
	var request BatchAnalysisRequest

	if err := c.ShouldBindJSON(&request); err != nil {
		errorMsg := ErrorMsg{
			Msg:   "請求參數錯誤",
			Error: err.Error(),
		}
		ErrorLog(errorMsg)
		c.JSON(http.StatusBadRequest, errorMsg)
		return
	}

	// 驗證分析方法
	if !isValidAnalysisMethod(request.Method) {
		errorMsg := ErrorMsg{
			Msg:   "無效的分析方法",
			Error: fmt.Sprintf("不支援的分析方法: %s", request.Method),
		}
		ErrorLog(errorMsg)
		c.JSON(http.StatusBadRequest, errorMsg)
		return
	}

	// 驗證彩種
	if !isValidLottoType(request.LottoType) {
		errorMsg := ErrorMsg{
			Msg:   "無效的彩種",
			Error: fmt.Sprintf("不支援的彩種: %s", request.LottoType),
		}
		ErrorLog(errorMsg)
		c.JSON(http.StatusBadRequest, errorMsg)
		return
	}

	// 驗證推算期數範圍
	if request.BatchPeriods < 1 || request.BatchPeriods > 100 {
		errorMsg := ErrorMsg{
			Msg:   "推算期數超出範圍",
			Error: "推算期數必須在 1-100 之間",
		}
		ErrorLog(errorMsg)
		c.JSON(http.StatusBadRequest, errorMsg)
		return
	}

	// 執行批次分析
	results, err := performBatchAnalysis(&request)
	if err != nil {
		errorMsg := ErrorMsg{
			Msg:   "批次分析失敗",
			Error: err.Error(),
		}
		ErrorLog(errorMsg)
		c.JSON(http.StatusInternalServerError, errorMsg)
		return
	}

	response := BatchAnalysisResponse{
		Success:     true,
		Message:     "批次分析完成",
		TotalCount:  len(results),
		Results:     results,
		GeneratedAt: time.Now(),
	}

	c.JSON(http.StatusOK, response)
}

// performBatchAnalysis 執行批次分析
func performBatchAnalysis(request *BatchAnalysisRequest) ([]BatchAnalysisResult, error) {
	var results []BatchAnalysisResult

	// 解析參考日期
	referenceDate, err := time.Parse("2006-01-02", request.ReferenceDate)
	if err != nil {
		return nil, fmt.Errorf("無效的參考日期格式: %v", err)
	}

	// 根據推算期數生成結果
	for i := 0; i < request.BatchPeriods; i++ {
		// 計算當前分析的日期（往前推算）
		currentDate := referenceDate.AddDate(0, 0, -i)
		
		// 生成分析結果
		result, err := generateAnalysisResult(request, currentDate, i)
		if err != nil {
			return nil, fmt.Errorf("生成第 %d 期分析結果失敗: %v", i+1, err)
		}

		results = append(results, result)
	}

	return results, nil
}

// generateAnalysisResult 生成單期分析結果
func generateAnalysisResult(request *BatchAnalysisRequest, date time.Time, index int) (BatchAnalysisResult, error) {
	result := BatchAnalysisResult{
		Period:     fmt.Sprintf("%04d%02d%02d", date.Year(), date.Month(), date.Day()),
		Date:       date.Format("2006-01-02"),
		Method:     request.Method,
		LottoType:  request.LottoType,
		Parameters: request.Parameters,
		Results:    make(map[string]interface{}),
	}

	// 根據不同的分析方法生成對應的結果
	switch request.Method {
	case "ball-follow":
		err := generateBallFollowResult(&result, request.Parameters)
		if err != nil {
			return result, err
		}
	case "tail":
		err := generateTailAnalysisResult(&result, request.Parameters)
		if err != nil {
			return result, err
		}
	case "pattern":
		err := generatePatternAnalysisResult(&result, request.Parameters)
		if err != nil {
			return result, err
		}
	default:
		return result, fmt.Errorf("不支援的分析方法: %s", request.Method)
	}

	return result, nil
}

// generateBallFollowResult 生成拖牌分析結果
func generateBallFollowResult(result *BatchAnalysisResult, params map[string]interface{}) error {
	// 從參數中提取拖牌分析所需的參數
	num1, _ := params["num1"].(float64)
	num2, _ := params["num2"].(float64)
	num3, _ := params["num3"].(float64)

	// 模擬生成拖牌分析結果
	result.Results["first_numbers"] = generateRandomNumbers(int(num1), 49)
	result.Results["second_numbers"] = generateRandomNumbers(int(num2), 49)
	result.Results["target_numbers"] = generateRandomNumbers(int(num3), 49)
	result.Results["probability"] = fmt.Sprintf("%.2f%%", generateRandomFloat(0, 100))
	result.Results["gap"] = generateRandomInt(1, 20)
	result.Results["target_gap"] = generateRandomInt(1, 10)

	return nil
}

// generateTailAnalysisResult 生成尾數分析結果
func generateTailAnalysisResult(result *BatchAnalysisResult, params map[string]interface{}) error {
	// 從參數中提取尾數分析所需的參數
	num1, _ := params["num1"].(float64)
	num3, _ := params["num3"].(float64)

	// 模擬生成尾數分析結果
	result.Results["tail_numbers"] = generateRandomNumbers(int(num1), 10)
	result.Results["predict_tails"] = generateRandomNumbers(int(num3), 10)
	result.Results["probability"] = fmt.Sprintf("%.2f%%", generateRandomFloat(0, 100))
	result.Results["frequency"] = generateRandomInt(1, 50)

	return nil
}

// generatePatternAnalysisResult 生成版路分析結果
func generatePatternAnalysisResult(result *BatchAnalysisResult, params map[string]interface{}) error {
	// 模擬生成版路分析結果
	result.Results["pattern"] = fmt.Sprintf("模式%d", generateRandomInt(1, 10))
	result.Results["prediction"] = generateRandomNumbers(6, 49)
	result.Results["confidence"] = fmt.Sprintf("%.2f%%", generateRandomFloat(0, 100))
	result.Results["trend"] = []string{"上升", "下降", "穩定"}[generateRandomInt(0, 2)]

	return nil
}

// 輔助函數
func isValidAnalysisMethod(method string) bool {
	validMethods := []string{"ball-follow", "tail", "pattern"}
	for _, valid := range validMethods {
		if method == valid {
			return true
		}
	}
	return false
}

func isValidLottoType(lottoType string) bool {
	validTypes := []string{"superlotto638", "lotto649", "daily539", "hklotto"}
	for _, valid := range validTypes {
		if lottoType == valid {
			return true
		}
	}
	return false
}

func generateRandomNumbers(count, max int) []int {
	numbers := make([]int, count)
	for i := 0; i < count; i++ {
		numbers[i] = generateRandomInt(1, max)
	}
	return numbers
}

func generateRandomInt(min, max int) int {
	return min + int(time.Now().UnixNano())%(max-min+1)
}

func generateRandomFloat(min, max float64) float64 {
	return min + (max-min)*float64(time.Now().UnixNano()%1000)/1000.0
}
