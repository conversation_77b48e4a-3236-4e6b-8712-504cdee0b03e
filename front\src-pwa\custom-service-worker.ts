/*
 * This file (which will be your service worker)
 * is picked up by the build system ONLY if
 * quasar.config.js > pwa > workboxMode is set to "injectManifest"
 */

// 定義 Service Worker 相關的類型
interface ServiceWorkerMessage {
  type: string;
  [key: string]: unknown;
}

interface ServiceWorkerClient {
  postMessage(message: ServiceWorkerMessage): void;
  id: string;
  url: string;
}

interface ServiceWorkerClients {
  matchAll(): Promise<readonly ServiceWorkerClient[]>;
  claim(): Promise<void>;
}

declare const self: ServiceWorkerGlobalScope &
  typeof globalThis & {
    skipWaiting: () => void;
    clients: ServiceWorkerClients;
  };

import { clientsClaim } from 'workbox-core';
import {
  precacheAndRoute,
  cleanupOutdatedCaches,
  createHandlerBoundToURL,
} from 'workbox-precaching';
import { registerRoute, NavigationRoute } from 'workbox-routing';

self.skipWaiting();
clientsClaim();

// 監聽來自主線程的消息
self.addEventListener('message', (event) => {
  if (event.data && event.data.type === 'SKIP_WAITING') {
    self.skipWaiting();
  }
  if (event.data && event.data.type === 'RELOAD_PAGE') {
    // 通知所有客戶端重新載入頁面
    self.clients.matchAll().then((clients: readonly ServiceWorkerClient[]) => {
      clients.forEach((client: ServiceWorkerClient) => {
        client.postMessage({ type: 'RELOAD_PAGE' });
      });
    });
  }
});

// Use with precache injection
precacheAndRoute(self.__WB_MANIFEST);

cleanupOutdatedCaches();

// Non-SSR fallback to index.html
// Production SSR fallback to offline.html (except for dev)
if (process.env.MODE !== 'ssr' || process.env.PROD) {
  registerRoute(
    new NavigationRoute(
      createHandlerBoundToURL(process.env.PWA_FALLBACK_HTML),
      { denylist: [/sw\.js$/, /workbox-(.)*\.js$/] }
    )
  );
}
