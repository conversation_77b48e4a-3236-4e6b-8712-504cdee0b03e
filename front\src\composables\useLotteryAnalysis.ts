import { ref, computed } from 'vue';
import {
  DrawResult,
  AnalysisConfig,
  ProgressInfo,
  WarningInfo,
} from '@/models/types';
import { LottoAnalyzer } from './analyzer';
import { LottoItem } from '@/api/modules/lotto';

export const useLotteryAnalysis = (initialResults?: DrawResult[]) => {
  const drawResults = ref<DrawResult[]>(initialResults || []);
  const analysisConfig = ref<AnalysisConfig>({
    firstGroupSize: 1,
    secondGroupSize: 1,
    targetGroupSize: 1,
    maxRange: 20,
    lookAheadCount: 1,
  });

  const analyzer = computed(() => new LottoAnalyzer(drawResults.value));

  const analyzeWithProgress = async (
    onProgress: (progress: ProgressInfo) => void,
    onWarning?: (warning: WarningInfo) => void
  ) => {
    analyzer.value.setProgressCallback(onProgress);
    if (onWarning) {
      analyzer.value.setWarningCallback(onWarning);
    }
    return analyzer.value.analyzeBatch(analysisConfig.value);
  };

  const setConfig = (config: Partial<AnalysisConfig>) => {
    analysisConfig.value = {
      ...analysisConfig.value,
      ...config,
    };
  };

  const setResults = (results: DrawResult[]) => {
    drawResults.value = results;
  };

  const init = (config: Partial<AnalysisConfig>, results: DrawResult[]) => {
    analysisConfig.value = {
      ...analysisConfig.value,
      ...config,
    };

    drawResults.value = results;
  };

  const stopAnalyzer = () => {
    analyzer.value.stopWorker();
  };

  const getTailSet = (item: LottoItem, isSuperLotto: boolean) => {
    const tailSet = new Set<number>();
    const numbers = [...item.draw_number_size];

    if (!isSuperLotto && item.special_number) {
      numbers.push(item.special_number);
    }

    for (const number of numbers) {
      tailSet.add(number % 10);
    }

    const tailSorted = Array.from(tailSet).sort((a, b) => {
      if (a === 0) return 1;
      if (b === 0) return -1;
      return a - b;
    });

    return tailSorted;
  };

  return {
    drawResults,
    analysisConfig,
    analyzeWithProgress,
    stopAnalyzer,
    init,
    setConfig,
    setResults,
    getTailSet,
  };
};
