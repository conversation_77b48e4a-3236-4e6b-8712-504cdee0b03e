<template>
  <q-page class="justify-center">
    <q-card>
      <q-card-section>
        <template v-if="store.getLotto?.draw_date">
          <div class="row lto-ref q-mb-sm">
            <div class="col-12 col-sm-4 self-center text-h6">
              <div>{{ store.getDrawLabel }}</div>
              <span>參考期號：</span>
              <span>{{ lottoRefer?.period }}</span>
              <span>（{{ lottoRefer?.draw_date }}）</span>
            </div>
            <div class="col-12 col-sm-6 self-center text-subtitle1">
              <div class="row balls">
                <div
                  class="col-auto"
                  v-for="number in lottoRefer?.draw_number_size"
                  :key="number"
                >
                  <div class="ball">
                    {{ paddingZero(number) }}
                  </div>
                </div>
                <div class="col-auto" v-if="lottoRefer?.special_number">
                  <div
                    class="ball special-number"
                    :key="lottoRefer?.special_number"
                  >
                    {{ paddingZero(lottoRefer?.special_number) }}
                  </div>
                </div>
              </div>
            </div>
          </div>
          <!-- 重新選擇參考 -->
          <div class="row q-mb-md">
            <div class="col">
              <q-btn
                type="button"
                label="重新選擇"
                color="primary"
                class="text-h6 q-ml-md"
                @click="reselectRef"
                v-if="!isReselect"
              />
              <q-btn
                type="button"
                label="取消選擇"
                color="negative"
                class="text-h6 q-ml-md"
                @click="cancelReselect"
                v-else
              />
            </div>
          </div>
        </template>
        <template v-else>
          <div class="row q-mb-md">
            <div class="text-h6">※請選擇參考期號</div>
          </div>
        </template>

        <q-separator class="q-mb-md" />

        <template v-if="!isReselect && store.getLotto?.draw_date">
          <div class="row q-mb-md">
            <div class="col-12 text-h5 text-weight-bolder text-center">
              拖牌設定
            </div>
          </div>

          <div class="row q-mb-md">
            <div class="col-12 text-h6 text-weight-bold">拖牌組合</div>
            <div class="col-12 col-sm-4 q-pa-sm">
              <q-select
                outlined
                dense
                v-model="num1"
                :options="numOpts"
                emit-value
                map-options
              />
            </div>
            <div class="col-12 col-sm-4 q-pa-sm">
              <q-select
                outlined
                dense
                v-model="num2"
                :options="numOpts"
                emit-value
                map-options
              />
            </div>
            <div class="col-12 col-sm-4 q-pa-sm">
              <q-select
                outlined
                dense
                v-model="num3"
                :options="numOpts"
                emit-value
                map-options
              />
            </div>
          </div>

          <div class="row q-mb-md">
            <!-- 推算期數 -->
            <div class="col-12 col-sm-4">
              <div class="text-h6 text-weight-bold">推算期數</div>
              <div class="q-pa-sm">
                <q-select
                  outlined
                  dense
                  v-model="periodNum"
                  :options="periodNumOpts"
                  input-debounce="0"
                  use-input
                  hide-selected
                  fill-input
                  @filter="onPeriodNumFilter"
                  emit-value
                  map-options
                >
                  <template v-slot:no-option>
                    <q-item>
                      <q-item-section class="text-grey">
                        無可用選項
                      </q-item-section>
                    </q-item>
                  </template>
                </q-select>
              </div>
            </div>
            <!-- 最大區間 -->
            <div class="col-12 col-sm-4">
              <div class="text-h6 text-weight-bold">最大區間</div>
              <div class="q-pa-sm">
                <q-select
                  outlined
                  dense
                  v-model="maxRange"
                  :options="maxRangeOpts"
                  emit-value
                  map-options
                />
              </div>
            </div>
            <!-- 預測期數 -->
            <div class="col-12 col-sm-4">
              <div class="text-h6 text-weight-bold">預測期數</div>
              <div class="q-pa-sm">
                <q-select
                  outlined
                  dense
                  v-model="aheadNum"
                  :options="aheadNumOpts"
                  emit-value
                  map-options
                />
              </div>
            </div>
          </div>

          <!-- 按鈕 -->
          <q-card-actions align="right" class="q-my-lg q-py-none q-px-md">
            <q-btn
              type="button"
              label="中斷計算"
              color="negative"
              @click="stopCalculating"
              class="text-h6 q-mr-md"
              v-if="progressStore.isCalculating"
            />

            <q-btn
              type="button"
              label="開始計算"
              color="positive"
              class="text-h6 q-px-lg q-py-sm"
              @click="doBallFollow"
              :loading="progressStore.isCalculating"
            >
              <template v-slot:loading>
                <q-spinner-dots />
              </template>
            </q-btn>
          </q-card-actions>
        </template>

        <IndexPage
          :draw-type-query="store.drawType"
          :date-query="lottoRefer?.draw_date || ''"
          :is-select-ref="true"
          @select-ref="selectRef"
          v-else
        />
      </q-card-section>

      <!-- 記憶體警告 -->
      <!-- <q-card-section v-if="progressStore.isCalculating">
        <MemoryWarning />
      </q-card-section> -->

      <!-- 進度條 -->
      <q-card-section v-if="progressStore.isCalculating">
        <div class="text-center q-mb-sm">
          {{ progressStore.progressMessage }}
        </div>
        <q-linear-progress
          rounded
          size="md"
          :value="progressStore.progress"
          :animation-speed="50"
          color="primary"
          class="q-mb-xs"
        />
      </q-card-section>
    </q-card>

    <!-- 結果顯示區域 -->
    <BallFollowResult
      v-if="!progressStore.isCalculating && drawResults.length > 0"
      :max-number="maxNumber"
      :is-super-lotto="isSuperLotto"
      :draw-results="drawResults"
      :predict-result="predictResult"
      :results="rdResults"
      :occurrence-results="rdOccurrenceResults"
      :page-size="50"
      @view-details="showDetails"
    />

    <!-- 詳細資料對話框 -->
    <BallFollowDetail
      v-model="detailsDialog"
      :results="drawResults"
      :selected-detail="selectedDetail"
      :occurrences="rdOccurrenceResults"
    />
  </q-page>
</template>

<script lang="ts" setup>
import { ref, watch } from 'vue';
import { useProgressStore } from '@/stores/progress';
import { useLottoStore } from '@/stores/lotto';
import { useLotteryAnalysis } from '@/composables/useLotteryAnalysis';
import { LOTTO_API } from '@/api';
import { LottoItem } from '@/api/modules/lotto';
import { StatResult, Occurrence, ProgressInfo, WarningInfo } from '@/models/types';
import { paddingZero } from '@/utils';
import IndexPage from './IndexPage.vue';
import BallFollowResult from '@/components/BallFollowResult.vue';
import BallFollowDetail from '@/components/BallFollowDetail.vue';
// import MemoryWarning from '@/components/MemoryWarning.vue';

const progressStore = useProgressStore();
const store = useLottoStore();
const lottoRefer = ref<LottoItem | null>(store.getLotto);

const isReselect = ref(false);
const reselectRef = () => {
  isReselect.value = true;
};

const selectRef = () => {
  isReselect.value = false;
};

const cancelReselect = () => {
  isReselect.value = false;
};

watch(
  () => store.getLotto,
  (val) => {
    if (val) {
      lottoRefer.value = val;
    }
  }
);

watch(
  () => lottoRefer.value?.period,
  () => {
    drawResults.value = [];
  }
);

const { analyzeWithProgress, stopAnalyzer, setConfig, setResults } =
  useLotteryAnalysis();

const rdResults = ref<StatResult[]>([]);
const rdOccurrenceResults = ref<Map<string, Occurrence>>(new Map()); // 用於存儲出現次數
const rdMatchResults = ref<Map<number, StatResult[]>>(new Map()); // 用於存儲匹配結果

const detailsDialog = ref(false);
const selectedDetail = ref<StatResult | null>(null);

const showDetails = (result: StatResult) => {
  selectedDetail.value = result;
  detailsDialog.value = true;
};

// 組合
const numOpts = [
  { label: '一星組合', value: 1 },
  { label: '二星組合', value: 2 },
  { label: '三星組合', value: 3 },
];

const num1 = ref(1);
const num2 = ref(1);
const num3 = ref(1);

// 推算期數(10-1000)
let periodNumOpts = ref(
  Array.from({ length: 991 }, (_, i) => ({
    label: `${i + 10}期`,
    value: i + 10,
  }))
);

const periodNum = ref(50);

const onPeriodNumFilter = (
  val: string,
  update: (callbackFn: () => void, afterFn?: (ref: unknown) => void) => void,
  abort: () => void
) => {
  const num = parseInt(val, 10);
  if (num < 10 || num > 1000) {
    abort();
  }

  update(() => {
    periodNumOpts.value = Array.from({ length: 991 }, (_, i) => i + 10)
      .filter((n) => n.toString().startsWith(val))
      .map((n) => ({ label: `${n.toString()}期`, value: n }));
  });
};

// 最大區間(10-30)
const maxRangeOpts = Array.from({ length: 21 }, (_, i) => ({
  label: `${i + 10}期`,
  value: i + 10,
}));

const maxRange = ref(20);

// 預測期數
const aheadNumOpts = Array.from({ length: 15 }, (_, i) => ({
  label: `下${i + 1}期`,
  value: i + 1,
}));

const aheadNum = ref(1);

const drawResults = ref<LottoItem[]>([]);
const predictResult = ref<LottoItem>({
  period: '',
  draw_date: '',
  draw_number_appear: [],
  draw_number_size: [],
  tails: new Map(),
});
let aheadCount = 1; // 預測期數

const maxNumber = ref(49);
const isSuperLotto = ref(false);
const doBallFollow = async () => {
  try {
    progressStore.startCalculating();
    rdResults.value = [];
    rdMatchResults.value = new Map();
    aheadCount = aheadNum.value;
    maxNumber.value = store.getMaxNumber;
    isSuperLotto.value = store.isSuperLotto;

    const response = await LOTTO_API.getLottoList({
      draw_type: store.getDrawType,
      date_end: lottoRefer.value?.draw_date ?? '',
      limit: periodNum.value,
    });

    drawResults.value = response.data;

    const predictResponse = await LOTTO_API.getLottoPredict({
      draw_type: store.getDrawType,
      draw_date: store.getLotto?.draw_date,
      ahead_count: aheadCount,
    });

    predictResult.value = predictResponse.data;

    if (predictResult.value.period) {
      const tailSet = new Set<number>();
      const predictNumbers = [...predictResult.value.draw_number_size];

      if (!isSuperLotto.value && predictResult.value.special_number) {
        predictNumbers.push(predictResult.value.special_number);
      }

      for (const number of predictNumbers) {
        tailSet.add(number % 10);
      }

      const tailSorted = Array.from(tailSet).sort((a, b) => {
        if (a === 0) return 1;
        if (b === 0) return -1;
        return a - b;
      });
      predictResult.value.tailSet = tailSorted;
    }

    // 創建純粹的數據結構
    const serializedDrawResults = drawResults.value
      .map((item: LottoItem) => {
        const nums = [...item.draw_number_size];
        if (item.special_number && !store.isSuperLotto) {
          // 除威力彩，其餘有彩種皆須加入特別號
          nums.push(item.special_number);
        }
        // 返回一個純粹的對象
        return {
          numbers: [...nums], // 確保創建新數組
          period: String(item.period), // 確保 period 是字符串
        };
      })
      .reverse();

    // 更新本地狀態
    setResults(serializedDrawResults);
    // 設置配置
    setConfig({
      firstGroupSize: num1.value,
      secondGroupSize: num2.value,
      targetGroupSize: num3.value,
      maxRange: maxRange.value,
      lookAheadCount: aheadNum.value,
    });

    // 使用防抖動的進度更新
    let lastUpdate = Date.now();
    const debounceInterval = 8; // 約 120 fps

    const results = await analyzeWithProgress(
      async (info: ProgressInfo) => {
        const now = Date.now();
        if (now - lastUpdate >= debounceInterval) {
          await progressStore.updateProgress(info);
          lastUpdate = now;
        }
      },
      (warning: WarningInfo) => {
        progressStore.addWarning(warning);
      }
    );

    rdResults.value = results.data;
    rdOccurrenceResults.value = results.occurrences;
    rdMatchResults.value = results.matchData;
  } catch (error) {
    console.error('數據載入失敗:', error);
  } finally {
    stopCalculating();
  }
};

const stopCalculating = () => {
  stopAnalyzer();
  progressStore.stopCalculating();
};
</script>

<style lang="scss" scoped>
@media (max-width: 767px) {
  .lto-ref {
    text-align: center;

    .balls {
      margin-top: 0.75rem;
      margin-bottom: 0.25rem;
      justify-content: center;
    }
  }
}
</style>
